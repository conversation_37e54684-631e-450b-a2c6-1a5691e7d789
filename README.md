# FastAPI WebSocket Chatbot with Supabase and Redis

A production-quality FastAPI application that serves as a WebSocket chatbot with Supabase PostgreSQL database and Redis caching. It includes Docker containerization and AWS ECS deployment via CI/CD.

## Project Structure

```
/app
├── main.py                      # FastAPI application initialization
├── api/
│   └── endpoints.py             # REST and WebSocket endpoints
├── models/
│   ├── message.py               # Pydantic models for messages
│   └── database.py              # Pydantic models for database tables
├── services/
│   └── ai_service.py            # AI service with PydanticAI agent integration
├── utils/
│   └── logger.py                # Custom logging setup
├── repository/
│   ├── db.py                    # Supabase PostgreSQL integration
│   └── cache.py                 # Redis cache implementation
└── tests/
    └── test_endpoints.py        # API tests
```

## Features

- REST API for conversation and property management
- WebSocket endpoint for real-time chat
- Supabase PostgreSQL database integration
- Redis caching for performance optimization
- PydanticAI agent-based AI responses
- Tool-based architecture for extensible AI functionality
- Docker containerization
- AWS ECS deployment via GitHub Actions CI/CD
- Comprehensive test suite

## Installation

1. Clone the repository
2. Set up environment variables in `.env` file (see `.env` for required variables)
3. Install dependencies:

```bash
pip install -r requirements.txt
```

## Running Locally

### Using Docker Compose

```bash
docker-compose up
```

### Using Python

```bash
python -m app.main
```

Or with uvicorn directly:

```bash
uvicorn app.main:app --reload
```

The server will run at `http://localhost:8000` by default.

## API Documentation

Once the server is running, access the interactive API documentation at:

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## API Endpoints

### Conversation Endpoints

- `POST /conversations` - Create a new conversation
- `GET /conversations` - List all conversations
- `GET /conversations/{conversation_id}` - Get a specific conversation
- `POST /conversations/{conversation_id}/messages` - Add a message to a conversation
- `DELETE /conversations/{conversation_id}` - Delete a conversation

### Property Endpoints

- `GET /properties` - List all properties
- `GET /properties/{property_id}` - Get a specific property

### WebSocket Endpoint

- `WS /ws/{client_id}/{conversation_id}` - Real-time chat connection

## WebSocket Usage Example

```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:8000/ws/client123/conversation456?user_id=user-uuid&portfolio_id=portfolio-uuid');

// Send a message
ws.send(JSON.stringify({ text: 'Tell me about my properties' }));

// Receive messages
ws.onmessage = (event) => {
  const response = JSON.parse(event.data);
  console.log('AI response:', response.content.text);
};
```

## Database Schema

The application integrates with the following Supabase database tables:

- `chat_messages` - Stores chat messages
- `prop` - Stores property information
- `prop_addresses` - Stores property addresses
- `prop_units` - Stores property unit details
- `prop_market_data` - Stores property market data

## Deployment

### Docker

Build and run the Docker container:

```bash
docker build -t chatbot-api .
docker run -p 8000:8000 chatbot-api
```

### CI/CD to AWS ECS

The included GitHub Actions workflow automatically deploys the application to AWS ECS when changes are pushed to the main branch. Configuration is in `.github/workflows/deploy.yml` and `.aws/task-definition.json`.

Required GitHub Secrets:

- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_REGION`
- `ECR_REPOSITORY`
- `ECS_CLUSTER_NAME`
- `ECS_SERVICE_NAME`

## Running Tests

```bash
pytest app/tests/
```

# Install dependencies and set up development environment
make setup

# Run code quality checks
make lint

# Run tests
make test

# Format code
make format

# Start development server
make dev

# Run with Docker Compose in development mode
docker-compose --profile dev up

# Build and run in production mode
docker-compose --profile prod up api-prod redis

# Deploy to AWS ECS (CI/CD will handle this automatically)
make deploy

# Generate requirements.txt from pyproject.toml
make requirements

# Sync your environment with requirements.txt
make sync

## PydanticAI Agent Integration

The application uses PydanticAI to provide an agent-based approach for the AI chat functionality. This enables:

- **Tool-based architecture**: The AI agent has access to specialized tools for different tasks
- **Document retrieval**: Coming soon - the ability to search and retrieve information from property documents
- **Property information tool**: Access to detailed property data from the user's portfolio
- **Direct response tool**: For handling simple conversational queries
- **Type-safe by design**: Leveraging Pydantic for validation and structured outputs

### Agent Configuration

The agent is defined in `app/services/agent_service.py` and includes:

- An OpenAI-based language model for decision making
- Multiple specialized tools for different tasks
- Context management for maintaining conversation history
- Strongly typed dependencies and outputs

### Environment Requirements

To use the PydanticAI agent, you need:

- OpenAI API key in your environment variables or `.env` file (`OPENAI_API_KEY`)
- PydanticAI dependencies (automatically installed via Docker or requirements.txt)

### Adding New Tools

To extend the agent with new capabilities:

1. Create a new method in `AgentService` class to implement the tool logic
2. Register the tool with the agent using the `@agent.tool` decorator or `agent.tool()` method
3. Ensure the tool has a clear description for the agent to understand when to use it
