"""
REST API endpoints module.
"""
# Python
import asyncio
import traceback
from datetime import datetime
from uuid import UUID, uuid4
from typing import Dict, Any, Optional, List, Union

# FastAPI
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, Response as DefaultResponse
from fastapi import APIRouter, HTTPException, status, Depends, Response, Body, Request, Path, BackgroundTasks, Query

# Utils
from app.utils.logger import get_logger
from app.utils.auth import get_token_from_header

# Datasources
from app.datasources.cherre import CherreDataService
from app.datasources.realtor import RealtorDataService

# Repository
from app.repository.cache import redis_cache

# Models
from app.models.ai_responses import PropertyAISummary, SlidesResponse

# Agents
from app.agents.financials import FinancialServiceAgent

# Services
from app.services.ai_service import ai_service
from app.services.chat_service import chat_service
from app.services.stripe_service import stripe_service
from app.services.document_service import document_service
from app.services.financials_service import PropertFinancialService
from app.services.property_service import PropertyService, PropertySearchService

logger = get_logger(__name__)

# Create router with no prefix (the parent router already has /api prefix)
router = APIRouter()

# Background task function for financial calculations
async def _perform_financial_calculation_background(
    task: str,
    prop_id: str,
    portfolio_id: str,
    year: int,
    override_user_input: bool,
    return_projections: bool,
    task_token: str,
    prop_fin_df,
    financial_service,
    prop_fin
) -> None:
    """
    Background function to perform financial calculations asynchronously.
    Updates Redis with progress and results.
    """
    try:
        # Update status to processing
        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="processing",
            data={
                "progress": {
                    "percentage": 5,
                    "operation": "Starting financial calculation...",
                    "step": 1,
                    "total_steps": 6,
                    "started_at": datetime.now().isoformat()
                }
            }
        )

        
        # AI agent will handle progress reporting internally
        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="processing",
            data={
                "progress": {
                    "percentage": 15,
                    "operation": f"Starting {task} financial analysis with AI agent...",
                    "step": 2,
                    "total_steps": 8,
                    "current_task": task,
                    "agent_active": True
                }
            }
        )

        # Pass task_token to financial service so agent can report progress
        fetched_df = await financial_service.process_financial_requests(
            task_type=task,
            prop_id=prop_id,
            year=year,
            portfolio_id=portfolio_id,
            prop_fin_df=prop_fin_df,
            return_projections=return_projections,
            task_token=task_token  # Pass task_token for progress tracking
        )

        if fetched_df is None:
            await redis_cache.set_task_queue(
                stage="calculation",
                token=task_token,
                status="failed",
                data={"error": f"Failed to fetch financial data for property {prop_id}"}
            )
            return

        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="processing",
            data={
                "progress": {
                    "percentage": 70,
                    "operation": "Computing property financials...",
                    "step": 6,
                    "total_steps": 8,
                    "current_task": task,
                    "agent_active": False
                }
            }
        )

        computed_df = await prop_fin.compute_property_financials(fetched_df, override_user_input=override_user_input)

        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="processing",
            data={
                "progress": {
                    "percentage": 85,
                    "operation": "Retrieving financial data sources...",
                    "step": 7,
                    "total_steps": 8,
                    "current_task": task
                }
            }
        )

        sources = await prop_fin.get_property_financials_sources(prop_id=prop_id, year=year)

        result_data = {
            "success": True,
            "task": task,
            "property_id": str(prop_id),
            "portfolio_id": str(portfolio_id),
            "year": year,
            "data": computed_df.to_dict() if computed_df is not None else None,
            "sources": sources.to_dict() if sources is not None and hasattr(sources, 'to_dict') else sources
        }

        # Update status to completed with results (use jsonable_encoder to handle UUIDs)
        completion_data = jsonable_encoder(result_data)
        completion_data["progress"] = {
            "percentage": 100,
            "operation": "Financial calculation completed successfully",
            "step": 8,
            "total_steps": 8,
            "current_task": task,
            "completed_at": datetime.now().isoformat()
        }

        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="completed",
            data=completion_data
        )

        logger.info(f"Financial calculation completed successfully for task {task_token}")

    except Exception as e:
        logger.error(f"Error in background financial calculation {task_token}: {str(e)}")
        await redis_cache.set_task_queue(
            stage="calculation",
            token=task_token,
            status="failed",
            data={"error": str(e)}
        )

# Helper function to handle datetime serialization
def serialize_response(obj: Any) -> Dict[str, Any]:
    """Serialize an object to JSON, handling datetime objects."""
    return jsonable_encoder(obj)

def standardize_message_format(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Standardize message format to include image_urls in the content object.
    
    Args:
        message: The message dict to standardize
        
    Returns:
        Dict: Standardized message with content.image_urls if applicable
    """
    # Extract image URLs from attachments if present
    if message.get("attachments") and isinstance(message["attachments"], dict):
        image_urls = message["attachments"].get("image_urls", [])
        
        # Add or update content object
        if "content" not in message:
            message["content"] = {"text": message.get("message", "")}
        else:
            message["content"]["text"] = message.get("message", "")
        
        # Add image_urls to content if present
        if image_urls:
            message["content"]["image_urls"] = image_urls
            
    # Ensure content object exists even if no images
    elif "content" not in message:
        message["content"] = {"text": message.get("message", "")}
        
    return message

# --- Chat Endpoints ---

@router.post("/chat", status_code=status.HTTP_201_CREATED, tags=["Chat"])
async def create_chat(
    portfolio_id: UUID,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Create a new chat for a portfolio.
    
    Args:
        portfolio_id: Portfolio ID to create chat for
    
    Returns:
        Dict: The newly created chat
    """
    user_id = token.get("sub")
    logger.info(f"User {user_id} creating new chat for portfolio {portfolio_id}")
    
    try:
        # Check portfolio access before creating chat
        if user_id:
            has_access = await chat_service.check_portfolio_access(str(portfolio_id), str(user_id))
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied to portfolio {portfolio_id}"
                )

        # Create chat
        chat = await chat_service.create_chat(str(portfolio_id), str(user_id) if user_id else "")
        
        # Invalidate user-specific portfolio chats cache
        if user_id:
            cache_key = f"portfolio_chats:{portfolio_id}:{user_id}"
            await redis_cache.delete(cache_key)
        
        return chat
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating chat: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"error": f"Failed to create chat: {str(e)}"}


@router.get("/chat", response_model=Dict[str, Any], summary="Get active chat", tags=["Chat"])
async def get_active_chat(
    portfolio_id: UUID,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Get the active chat for a portfolio.
    """
    try:
        # Extract user_id and use per-user-per-portfolio logic
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user ID in token"
            )

        # Check cache first
        cache_key = f"chat_list:{portfolio_id}:{user_id}"  # Add user_id to cache key
        cached_chat = await redis_cache.get(cache_key)
        if cached_chat:
            if "messages" in cached_chat:
                for i, msg in enumerate(cached_chat["messages"]):
                    cached_chat["messages"][i] = standardize_message_format(msg)
            return cached_chat

        # Cache miss - fetch from database using new per-user logic
        chat = await chat_service.get_or_create_user_portfolio_chat(str(portfolio_id), str(user_id))
        if not chat:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to portfolio {portfolio_id}"
            )
        
        if "messages" in chat:
            for i, msg in enumerate(chat["messages"]):
                chat["messages"][i] = standardize_message_format(msg)
        
        # Cache the result with 1-day TTL
        await redis_cache.set(cache_key, chat)
        await redis_cache.expire(cache_key, 86400)
        
        return chat
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting active chat for portfolio {portfolio_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"error": f"Failed to get active chat: {str(e)}"}


@router.get("/chats", response_model=Dict[str, Any], summary="Get all chats for a portfolio", tags=["Chat"])
async def get_portfolio_chats(
    portfolio_id: UUID,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Get all active chats for a portfolio.
    
    Args:
        portfolio_id: Portfolio ID to get chats for
        
    Returns:
        Dict: List of chats for the portfolio
    """
    try:
        # Extract user_id and use per-user-per-portfolio logic
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user ID in token"
            )

        # Check cache first
        cache_key = f"portfolio_chats:{portfolio_id}:{user_id}"  # Add user_id to cache key
        cached_chats = await redis_cache.get(cache_key)
        if cached_chats:
            if "chats" in cached_chats:
                for chat in cached_chats["chats"]:
                    if "messages" in chat:
                        for i, msg in enumerate(chat["messages"]):
                            chat["messages"][i] = standardize_message_format(msg)
            return cached_chats

        # Cache miss - fetch from database using new per-user logic
        chats = await chat_service.get_chats_by_portfolio_and_user(str(portfolio_id), str(user_id))
        if not chats or len(chats) == 0:
            return {"chats": []}
        
        for chat in chats:
            if "messages" in chat:
                for i, msg in enumerate(chat["messages"]):
                    chat["messages"][i] = standardize_message_format(msg)
        
        # Cache the result with 1-day TTL
        result = {"chats": chats}
        await redis_cache.set(cache_key, result)
        await redis_cache.expire(cache_key, 86400)
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chats for portfolio {portfolio_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"error": f"Failed to get portfolio chats: {str(e)}"}


@router.get("/chat/{chat_id}", summary="Get chat messages", tags=["Chat"])
async def get_chat(
    chat_id: str,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Get a chat by ID.
    
    Args:
        chat_id: ID of the chat to retrieve
        
    Returns:
        Dict: The requested chat
        
    Raises:
        HTTPException: If the chat is not found or user has no access
    """
    try:
        # Extract user_id from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user ID in token"
            )
        
        # Check cache first
        cache_key = f"chat_messages:{chat_id}"
        cached_chat = await redis_cache.get(cache_key)
        if cached_chat:
            # Still need to verify access for cached results
            has_access = await chat_service.check_chat_access(chat_id, user_id)
            if not has_access:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User does not have access to this chat"
                )
                
            if "messages" in cached_chat:
                for i, msg in enumerate(cached_chat["messages"]):
                    cached_chat["messages"][i] = standardize_message_format(msg)
            
            return cached_chat

        # Cache miss - fetch from database with access check
        chat = await chat_service.get_chat_with_access_check(chat_id, user_id)
        if not chat:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chat with ID {chat_id} not found or access denied"
            )
        
        if "messages" in chat:
            for i, msg in enumerate(chat["messages"]):
                chat["messages"][i] = standardize_message_format(msg)
        
        # Cache the result with 1-day TTL
        await redis_cache.set(cache_key, chat)
        await redis_cache.expire(cache_key, 86400)
        
        return chat
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chat {chat_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"error": f"Failed to get chat: {str(e)}"}


@router.delete("/chat/{chat_id}", tags=["Chat"])
async def delete_chat(
    chat_id: str,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Delete a chat by ID.
    
    Args:
        chat_id: ID of the chat to delete
        
    Returns:
        Dict with success message
        
    Raises:
        HTTPException: If the chat is not found
    """
    try:
        # Extract user_id and check access
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid user ID in token"
            )

        # Check if user has access to this chat
        has_access = await chat_service.check_chat_access(chat_id, user_id)
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this chat"
            )

        # First get the chat to find its portfolio_id
        chat = await chat_service.get_chat(chat_id)
        portfolio_id = None
        if chat and "portfolio_id" in chat:
            portfolio_id = chat["portfolio_id"]
        
        success = await chat_service.delete_chat(chat_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chat with ID {chat_id} not found"
            )
            
        # Invalidate caches
        await redis_cache.delete(f"chat_messages:{chat_id}")
        
        # Also invalidate user-specific portfolio chat cache if we found the portfolio_id
        if portfolio_id and user_id:
            await redis_cache.delete(f"portfolio_chats:{portfolio_id}:{user_id}")
            await redis_cache.delete(f"chat_list:{portfolio_id}:{user_id}")
            
        # Return success message
        return {
            "success": True,
            "message": f"Chat {chat_id} deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting chat {chat_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to delete chat: {str(e)}"
        }


@router.post("/chat/{chat_id}/members", tags=["Chat"])
async def add_chat_member(
    chat_id: str,
    user_id: UUID,
    role: str = "member",
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Add a member to a chat.
    
    Args:
        chat_id: ID of the chat
        user_id: ID of the user to add
        role: Optional role for the user (default: "member")
        
    Returns:
        Dict with success message
    """
    try:
        success = await chat_service.add_chat_member(chat_id, str(user_id), role)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to add member to chat {chat_id}"
            )
        return {
            "success": True,
            "message": f"Added user {user_id} to chat {chat_id}"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding member to chat {chat_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to add member: {str(e)}"
        }


@router.delete("/chat/{chat_id}/members/{user_id}", tags=["Chat"])
async def remove_chat_member(
    chat_id: str,
    user_id: UUID,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Remove a member from a chat.
    
    Args:
        chat_id: ID of the chat
        user_id: ID of the user to remove
        
    Returns:
        Dict with success message
    """
    try:
        success = await chat_service.remove_chat_member(chat_id, str(user_id))
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to remove member from chat {chat_id}"
            )
        return {
            "success": True,
            "message": f"Removed user {user_id} from chat {chat_id}"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing member from chat {chat_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to remove member: {str(e)}"
        }


@router.delete("/chat/messages/{chat_id}", tags=["Chat"])
async def clear_chat_messages(
    chat_id: str,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Clear all messages in a chat by marking them as deleted.
    The chat itself is preserved, but all messages are hidden.
    
    Args:
        chat_id: ID of the chat to clear messages for
        
    Returns:
        Dict with success message
        
    Raises:
        HTTPException: If the chat is not found or another error occurs
    """
    try:
        success = await chat_service.clear_chat_messages(chat_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chat with ID {chat_id} not found or already deleted"
            )
        
        # Invalidate cache for this chat
        cache_key = f"chat_messages:{chat_id}"
        await redis_cache.delete(cache_key)
        
        # Return success message
        return {
            "success": True,
            "message": f"All messages in chat {chat_id} have been cleared"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing messages for chat {chat_id}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to clear chat messages: {str(e)}"
        }

### --- Redis Endpoints ---

@router.post("/props/cache", status_code=status.HTTP_200_OK, tags=["Cache"])
async def cache_property(
    portfolio_id: UUID,
    property_id: UUID,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Fetch property data from PostgreSQL and store in Redis cache.
    
    Args:
        portfolio_id: Portfolio ID for the property
        property_id: Property ID to cache
        
    Returns:
        Dict: Status of the cache operation
    """
    try:
        # Verify token (already done by the dependency)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or missing authentication token"
            )
        
        # Fetch property data using property_service instead of db_repo
        property_data = await PropertyService.get_property(str(property_id))
        
        if not property_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Property with ID {property_id} not found"
            )
        
        # Serialize the property data
        serialized_data = jsonable_encoder(property_data)
        
        # Create cache key using portfolio_id and property_id
        cache_key = f"prop_data:{portfolio_id}:{property_id}"
        
        # Store in Redis cache
        await redis_cache.set(cache_key, serialized_data)
        
        # Set TTL separately (1 day = 86400 seconds)
        await redis_cache.expire(cache_key, 86400)
        
        return {
            "success": True,
            "message": f"Property data cached with key: {cache_key}"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error caching property data: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to cache property data: {str(e)}"
        }
    

@router.post("/cache/prop-data", status_code=status.HTTP_200_OK, tags=["Cache"])
async def cache_comprehensive_property_data(
    request_data: Dict[str, Any] = Body(...),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> JSONResponse:
    """
    Fetch comprehensive property data from PostgreSQL and store in Redis cache.
    
    This endpoint retrieves property data with all related information (address, units, 
    market data, environmental risk, solar potential, air quality, points of interest,
    financials) and demographics, then stores the complete data structure in Redis.
    
    Request Body:
        portfolio_id: Portfolio ID for the property (UUID)
        property_id: Property ID to cache (UUID)
        
    Returns:
        JSONResponse: Status of the cache operation with details of included data
    """
    try:
        # Verify token (already done by the dependency)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or missing authentication token"
            )
        
        # Extract and validate IDs from request body
        try:
            portfolio_id = UUID(request_data.get("portfolio_id"))
            property_id = UUID(request_data.get("property_id"))
        except (ValueError, TypeError, AttributeError):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid portfolio_id or property_id format. Both must be valid UUIDs."
            )
        
        # Use property_service to get property data with all related information
        property_dict = await PropertyService.get_property_with_relations(str(property_id))
        
        if not property_dict:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Property with ID {property_id} not found"
            )
        
        # Fetch demographics data if address is available
        try:
            if property_dict.get("prop_address"):
                address = property_dict["prop_address"]
                # Handle both dict and Pydantic model cases
                if hasattr(address, "zip"):
                    zip_code = address.zip
                elif isinstance(address, dict) and "zip" in address:
                    zip_code = address["zip"]
                else:
                    # No zip code available
                    logger.warning("No zip code found in address data")
                    zip_code = None
                
                if zip_code:
                    # Use property_service to get demographics data
                    demographics = await PropertyService.get_zip_demographics(zip_code)
                    if demographics:
                        property_dict["zip_demographics"] = demographics
        except Exception as e:
            logger.warning(f"Failed to fetch demographics data: {str(e)}")
        
        # Serialize the property data
        serialized_data = jsonable_encoder(property_dict)
        
        # Create cache key using portfolio_id and property_id
        cache_key = f"full_prop_data:{portfolio_id}:{property_id}"
        
        # Store in Redis cache
        await redis_cache.set(cache_key, serialized_data)
        
        # Set TTL separately (1 day = 86400 seconds)
        await redis_cache.expire(cache_key, 86400)
        
        # Calculate included data types for response
        included_data = [key for key in [
            "prop_address", "prop_units", "prop_market_data", "prop_env_risk", 
            "prop_solar_potential", "prop_air_quality", "pois", 
            "prop_financials", "zip_demographics"
        ] if key in property_dict and property_dict[key]]
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": f"Cache key: {cache_key}",
                "ttl": 86400,  # seconds (1 day)
                "included_data": included_data
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error caching comprehensive property data: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "success": False,
                "error": f"Failed to cache property data: {str(e)}"
            }
        )
    

### --- Documents Endpoints ---

@router.get("/documents", tags=["Documents"])
async def get_portfolio_documents(
    portfolio_id: UUID = Query(..., description="Portfolio ID to fetch documents for"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Get all documents for a portfolio, organized in a hierarchical structure.
    
    This endpoint retrieves all documents associated with a portfolio and organizes them
    into a structured format for display in the Data Room view. Documents are grouped by
    property, with AI-generated slides at the top level.
    
    Note: Only returns non-deleted documents (is_deleted=False)
    
    Args:
        portfolio_id: Portfolio ID to fetch documents for
        
    Returns:
        Dict with portfolio details, slides, and documents organized by property
    """
    try:
        # Check cache first
        cache_key = f"portfolio_documents:{portfolio_id}"
        cached_docs = await redis_cache.get(cache_key)
        if cached_docs:
            return cached_docs

        # Cache miss - fetch from service
        result = await document_service.get_portfolio_documents(str(portfolio_id))
        
        # Cache the result with 3-day TTL
        await redis_cache.set(cache_key, result)
        await redis_cache.expire(cache_key, 259200)
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting portfolio documents: {str(e)}")
        logger.error(traceback.format_exc())
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"success": False, "error": f"Failed to get portfolio documents: {str(e)}"}

@router.post("/documents/process", tags=["Documents"])
async def process_documents_for_rag_endpoint(
    token: Dict[str, Any] = Depends(get_token_from_header),
    supabase_urls: List[str] = Body(..., description="List of URLs to documents in Supabase storage"),
    portfolio_id: str = Body(..., description="Portfolio ID to associate with documents"),
    user_id: str = Body(..., description="User ID of document owner"),
    existing_task_token: Optional[str] = Body(None, description="Existing task token to merge with (optional)"),
    response: Response = DefaultResponse()
) -> dict:
    """
    Ingest multiple documents for RAG: parse, embed, and store in vector DB with task tracking.
    
    This endpoint handles multiple document uploads with real-time status tracking. It processes
    documents in parallel and provides a task token for monitoring progress.
    
    Features:
    1. Parallel document processing for performance
    2. Redis-based task status tracking with real percentage
    3. Merging new uploads with existing processing tasks
    4. Individual document status (processing/complete/failed)
    5. Automatic cache invalidation upon completion
    
    Args:
        supabase_urls: List of direct URLs to documents in Supabase storage
        portfolio_id: Portfolio ID to use as vector DB namespace
        user_id: User ID of the document owner
        existing_task_token: Optional token to merge with existing upload task
        
    Returns:
        Dict with task token for status tracking and initial upload info
    """
    try:
        # Validate input
        if not supabase_urls:
            if response:
                response.status_code = status.HTTP_400_BAD_REQUEST
            return {"success": False, "error": "No document URLs provided"}
        
        if len(supabase_urls) > 20:  # Reasonable limit
            if response:
                response.status_code = status.HTTP_400_BAD_REQUEST
            return {"success": False, "error": "Too many documents. Maximum 20 documents per batch"}
        
        logger.info(f"Processing {len(supabase_urls)} documents for portfolio {portfolio_id}")
        
        # Start batch processing
        result = await document_service.process_documents_batch(
            document_urls=supabase_urls,
            portfolio_id=portfolio_id,
            user_id=user_id,
            existing_token=existing_task_token
        )
        
        return {
            "success": True,
            "task_token": result["task_token"],
            "total_documents": result["total_documents"],
            "message": result["message"]
        }
        
    except ValueError as e:
        logger.error(f"Validation error in document processing: {str(e)}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Error processing documents for RAG: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"success": False, "error": str(e)}


@router.get("/documents/process/status", tags=["Documents"])
async def get_document_processing_status(
    token: Dict[str, Any] = Depends(get_token_from_header),
    task_token: str = Query(..., description="Task token from the upload process"),
    portfolio_id: str = Query(..., description="Portfolio ID"),
    response: Response = DefaultResponse()
) -> dict:
    """
    Get the current status of a document processing task.
    
    This endpoint returns real-time status information for a batch document upload,
    including individual document statuses and overall progress percentage.
    
    Args:
        task_token: Unique task token returned from the upload endpoint
        portfolio_id: Portfolio ID to verify access
        
    Returns:
        Dict with task status, percentage complete, and individual document statuses
    """
    try:
        # Get task status from Redis
        task_status = await document_service.get_upload_status(portfolio_id, task_token)
        
        if not task_status:
            if response:
                response.status_code = status.HTTP_404_NOT_FOUND
            return {
                "success": False, 
                "error": "Task not found. Token may have expired or be invalid."
            }
        
        return {
            "success": True,
            "is_ready": task_status.get("is_ready", False),
            "percentage": task_status.get("percentage", 0),
            "total_documents": task_status.get("total_documents", 0),
            "completed_documents": task_status.get("completed_documents", 0),
            "failed_documents": task_status.get("failed_documents", 0),
            "documents": task_status.get("documents", [])
        }
        
    except Exception as e:
        logger.error(f"Error getting processing status: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"success": False, "error": str(e)}

@router.post("/documents/generate-slides", response_model=SlidesResponse, tags=["Documents"])
async def generate_portfolio_slides_endpoint(
    portfolio_id: str = Body(..., description="Portfolio ID to generate slides for"),
    user_id: str = Body(..., description="User ID who owns the portfolio"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> SlidesResponse:
    """
    Generate PDF slides for a portfolio, store in Supabase, and return access information.
    
    This endpoint:
    1. Processes all documents in the portfolio 
    2. Uses an LLM to generate HTML content
    3. Converts the HTML to PDF using WeasyPrint
    4. Uploads the PDF to Supabase storage
    5. Stores metadata in the slides_data table
    
    The generated slides include:
    - Title slide with portfolio name
    - Executive summary
    - Property-specific slides with key metrics
    - Document summaries grouped by type
    - Market analysis based on available data
    - Investment highlights and opportunities
    
    Args:
        portfolio_id: Portfolio ID to fetch documents from
        user_id: User ID who owns the portfolio
        
    Returns:
        SlidesResponse with PDF URL and metadata
    """
    try:
        # Use the new method that generates PDF and stores it
        result = await document_service.generate_and_store_pdf_slides(
            portfolio_id=portfolio_id
        )
        
        # Invalidate portfolio documents cache
        cache_key = f"portfolio_documents:{portfolio_id}"
        await redis_cache.delete(cache_key)
        logger.info(f"Invalidated cache for {cache_key} after generating slides")
        
        return result
        
    except Exception as e:
        logger.error(f"Error generating portfolio slides: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate slides: {str(e)}"
        )

@router.delete("/documents/{portfolio_id}/{document_id}", tags=["Documents"])
async def delete_document(
    portfolio_id: UUID = Path(..., description="Portfolio ID the document belongs to"),
    document_id: UUID = Path(..., description="Document ID to delete"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Soft delete a document by marking it as deleted in the database.
    
    This endpoint does not delete the document from Supabase storage,
    but marks it as deleted in the database, so it won't appear in document listings.
    
    Args:
        portfolio_id: Portfolio ID the document belongs to (for validation)
        document_id: Document ID to delete
        
    Returns:
        Dict with success status and message
    """
    try:
        # Call document service to perform the soft delete
        success = await document_service.delete_document(
            portfolio_id=str(portfolio_id),
            document_id=str(document_id)
        )
        
        if not success:
            if response:
                response.status_code = status.HTTP_404_NOT_FOUND
            return {
                "success": False,
                "message": f"Failed to delete document {document_id}"
            }
            
        # Return success
        return {
            "success": True,
            "message": f"Document {document_id} deleted successfully"
        }
        
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        logger.error(traceback.format_exc())
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {"success": False, "error": f"Failed to delete document: {str(e)}"}

### --- Financials Endpoints ---

@router.post("/finance/calculate/{task}", status_code=status.HTTP_200_OK, tags=["Financials"])
async def perform_financial_calculation(
    task: str = Path(..., description="Task to perform (e.g., 'recalculate')"),
    prop_address_id: UUID = Body(..., description="Property Address ID to perform calculations for"),
    portfolio_id: UUID = Body(..., description="Portfolio ID to perform calculations for"),
    year: int = Body(..., description="Year to perform calculations for"),
    override_user_input: bool = Body(default=False, description="Override user input for the calculation"),
    return_projections: bool = Body(default=True, description="Return projections for the calculation"),
    params: Dict[str, Any] = Body(default={}, description="Additional parameters for the calculation (e.g., 'year': 2023)"),
    async_mode: bool = Body(default=True, description="Run calculation in background and return task token"),
    task_token: str = Body(default="", description="Task token to run calculation in background"),
    token: Dict[str, Any] = Depends(get_token_from_header)
) -> Dict[str, Any]:
    """
    Universal endpoint for performing financial calculations on properties.
    
    """
    if task_token:
        data = await redis_cache.get_task_queue(stage="calculation", token=task_token)
        if data:
            return {"status": data.get("status"), "token": task_token, "data": data.get("data")}
        else:
            return {"status": "not_found", "token": task_token, "data": {}}
    
    if not prop_address_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Property Address ID is required"
        )
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Task is required"
        )

    # Get property ID from property address ID
    property = await PropertyService.get_property_by_address_id(prop_address_id)
    
    if not property:
        logger.warning(f"Property not found for address ID: {prop_address_id}")
        return None
    
    prop_id = property.id

    prop_fin = PropertFinancialService()
    prop_fin_df = await prop_fin.get_property_financials_dataframe(prop_id=str(prop_id))

    if prop_fin_df is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Property financials not found for property address {str(prop_id)}"
        )
    
    financial_service = FinancialServiceAgent()

    # If async_mode is enabled and task is NOT recalculate, run calculation in background
    # Force recalculate to run synchronously
    if async_mode and task != 'recalculate':
        task_token = str(uuid4())
        await redis_cache.set_task_queue(stage="calculation", token=task_token, status="started")

        # Create background task for financial calculation
        asyncio.create_task(
            _perform_financial_calculation_background(
                task=task,
                prop_id=str(prop_id),
                portfolio_id=str(portfolio_id),
                year=year,
                override_user_input=override_user_input,
                return_projections=return_projections,
                task_token=task_token,
                prop_fin_df=prop_fin_df,
                financial_service=financial_service,
                prop_fin=prop_fin
            )
        )

        return {
            "success": True,
            "message": "Financial calculation initiated successfully",
            "task_token": task_token,
            "status": "started",
            "task": task,
            "property_id": str(prop_id),
            "portfolio_id": str(portfolio_id),
            "year": year
        }

    # Synchronous execution (existing behavior)
    if task == 'recalculate':
        computed_df = await prop_fin.compute_property_financials(prop_fin_df, override_user_input=override_user_input)
        sources = await prop_fin.get_property_financials_sources(prop_id=str(prop_id), year=year)
        
        # Return the computed data
        return {
            "success": True,
            "task": task,
            "property_id": str(prop_id),
            "portfolio_id": str(portfolio_id),
            "year": year,
            "data": computed_df.to_dict() if computed_df is not None else None,
            "sources": sources
        }
    else:
        fetched_df = await financial_service.process_financial_requests(
            task_type=task, 
            prop_id=str(prop_id), 
            year=year, 
            portfolio_id=str(portfolio_id), 
            prop_fin_df=prop_fin_df,
            return_projections=return_projections
            )
        
        if fetched_df is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Failed to fetch financial data for property {prop_id}"
            )
        computed_df = await prop_fin.compute_property_financials(fetched_df, override_user_input=override_user_input)
        sources = await prop_fin.get_property_financials_sources(prop_id=str(prop_id), year=year)
        # Return the computed data
        return {
            "success": True,
            "task": task,
            "property_id": str(prop_id),
            "portfolio_id": str(portfolio_id),
            "year": year,
            "data": computed_df.to_dict() if computed_df is not None else None,
            "sources": sources
        }


@router.get("/finance/calculate-status/{task_token}", status_code=status.HTTP_200_OK, tags=["Financials"])
async def get_calculation_status(
    task_token: str = Path(..., description="Task token from the calculation request"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Poll the status of a financial calculation task.

    This endpoint should be called every 5 seconds by the UI to get real-time updates
    on the calculation progress. It returns detailed status information including
    what operation is currently being performed.

    Args:
        task_token: Unique task token returned from the calculation request

    Returns:
        Dict containing:
        - status: 'started', 'processing', 'completed', 'failed'
        - progress: Percentage complete (0-100)
        - current_operation: Description of current operation being performed
        - data: Results (only when status is 'completed')
        - error: Error message (only when status is 'failed')
        - estimated_time_remaining: Estimated seconds remaining (optional)
    """
    try:
        # Get task status from Redis
        task_data = await redis_cache.get_task_queue(stage="calculation", token=task_token)

        if not task_data:
            if response:
                response.status_code = status.HTTP_404_NOT_FOUND
            return {
                "success": False,
                "status": "not_found",
                "message": f"Task {task_token} not found or expired",
                "task_token": task_token
            }

        task_status = task_data.get("status", "unknown")
        task_result_data = task_data.get("data", {})

        # Base response
        status_response = {
            "success": True,
            "task_token": task_token,
            "status": task_status,
            "timestamp": task_data.get("timestamp", ""),
        }

        # Add status-specific information
        if task_status == "started":
            status_response.update({
                "progress": 0,
                "current_operation": "Initializing financial calculation...",
                "estimated_time_remaining": 30
            })
        elif task_status == "processing":
            # Get detailed progress info from task data
            progress_info = task_result_data.get("progress", {})
            status_response.update({
                "progress": progress_info.get("percentage", 10),
                "current_operation": progress_info.get("operation", "Processing financial data..."),
                "estimated_time_remaining": progress_info.get("eta_seconds", 20)
            })
        elif task_status == "completed":
            status_response.update({
                "progress": 100,
                "current_operation": "Calculation completed successfully",
                "data": task_result_data,
                "estimated_time_remaining": 0
            })
        elif task_status == "failed":
            status_response.update({
                "progress": 0,
                "current_operation": "Calculation failed",
                "error": task_result_data.get("error", "Unknown error occurred"),
                "estimated_time_remaining": 0
            })
        else:
            status_response.update({
                "progress": 0,
                "current_operation": f"Unknown status: {task_status}",
                "estimated_time_remaining": 0
            })

        return status_response

    except Exception as e:
        logger.error(f"Error getting calculation status for {task_token}: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "status": "error",
            "message": f"Failed to get task status: {str(e)}",
            "task_token": task_token
        }


## --- Property Endpoints ---

@router.post("/data/prop-ai-summary-short", response_model=PropertyAISummary, tags=["Property"])
async def generate_property_ai_summary(
    property_data: dict = Body(..., description="Property data as a JSON string"),
    response: Response = DefaultResponse()
) -> PropertyAISummary:
    """
    Generate an AI-powered summary of a property.
    
    This endpoint takes property data as a JSON string, processes it with GPT-4o,
    and returns a structured analysis including:
    1. Property summary
    2. Pros and cons
    3. Points of interest summary
    4. Financial prospects
    
    Args:
        property_data: Property data as a JSON string
        
    Returns:
        PropertyAISummary: Structured AI analysis of the property
        
    Raises:
        HTTPException: If authentication fails or data format is invalid
    """
    try:

        # Generate AI summary
        summary = await ai_service.generate_property_summary(property_data)
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating property AI summary: {str(e)}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        # Return a fallback response
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate property summary: {str(e)}"
        )

@router.post("/property/data/{data_type}", status_code=status.HTTP_200_OK, tags=["Property"])
async def get_property_data(
    background_tasks: BackgroundTasks,
    data_type: str = Path(..., description="Type of property data to fetch"),
    address: Optional[str] = Body(None, description="Full property address to fetch details for"),
    city: Optional[str] = Body(None, description="City to fetch details for"),
    state: Optional[str] = Body(None, description="State to fetch details for"),
    zipcode: Optional[str] = Body(None, description="Zipcode to fetch demographics for"),
    prop_id: Optional[UUID] = Body(None, description="Optional property ID to save data to database"),
    profile_id: Optional[UUID] = Body(None, description="Optional profile ID to fetch property units with AI"),
    task_token: Optional[str] = Body(None, description="Optional task token to fetch property units with AI"),
    async_mode: bool = Body(False, description="Run the fetch operation in the background without awaiting"),
    force_refresh: bool = Body(False, description="Force refresh the data even if it exists in the database"),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Fetch property data from Cherre for a given address.
    
    This endpoint fetches property data from the Cherre API and returns it as a dictionary.
    It supports fetching data for multiple data types including:
    
    - property_data: Detailed property information
    - multifamily_data: Multifamily property data
    - tax_history: Tax history data
    - demographics: Demographic data
    
    Args:
        data_type: Type of property data to fetch
        address: Full property address to fetch details for
        prop_id: Optional property ID to save data to database
        
    Returns:
        Dict containing property data
    """ 

    if task_token:
        data = await redis_cache.get_task_queue(stage="data", token=task_token)
        if data:
            return {"status": data.get("status"), "token": task_token, "data": data.get("data")}
        else:
            return {"status": "not_found", "token": task_token, "data": {}}
        

    # Mapping of data types to their fetch and save functions
    full_address = f"{address if address else None},{city if city else None},{state if state else None},{zipcode if zipcode else None}"

    data_mapping = {
        "property_data": {
            "fetch": CherreDataService.fetch_property_data,
            "params": {"full_address": full_address}
        },
        "multifamily_data": {
            "fetch": CherreDataService.fetch_multifamily_property_data,
            "params": {"full_address": full_address}
        },
        "tax_history": {
            "fetch": CherreDataService.fetch_tax_history,
            "params": {"full_address": full_address}
        },
        "demographics": {
            "fetch": CherreDataService.fetch_demographics,
            "params": {"zipcode": zipcode if zipcode else ""}
        },
        "market_data": {
            "fetch": RealtorDataService.get_listing_history,
            "params": {"full_address": full_address, "address": address if address else "", "zipcode": zipcode if zipcode else ""}
        },
        "unit_data": {
            "fetch": PropertySearchService.get_property_units_ai,
            "params": {"address": address if address else "", "city": city if city else "", "state": state if state else "", "zipcode": zipcode if zipcode else "", "task_token": None, "task_key": None, "force_refresh": force_refresh}
        }
    }

    data_type_config = data_mapping.get(data_type)

    if not data_type_config:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid data type: {data_type}"
        )
    
    try:
        if async_mode:
            task_token = str(uuid4())
            payload = await redis_cache.set_task_queue(stage="data", token=task_token, status="started")

            # Run the fetch operation in the background without awaiting
            kwargs = data_type_config["params"]
            kwargs["task_token"] = task_token
            kwargs["task_key"] = await redis_cache.task_queue_keygen(stage="data", task_token=task_token)
            
            # Schedule the fetch operation as a non-blocking asyncio background task
            asyncio.create_task(data_type_config["fetch"](**kwargs))

            return payload
        else:
            kwargs = data_type_config["params"]
            data = await data_type_config["fetch"](**kwargs)
            return data
    except Exception as e:
        logger.error(f"Error fetching {data_type} data: {str(e)} | {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch {data_type} data: {str(e)}"
        )
    
### --- Subscription Endpoints ---

@router.post("/subscription/webhook", tags=["Subscription"])
async def handle_stripe_webhook_event(
    request: Request,
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Handle a Stripe webhook event.
    
    This endpoint receives webhook notifications from Stripe 
    and processes them to update subscription statuses in our database.
    
    Note: This endpoint is public and does not require authentication
    because Stripe webhooks cannot provide authentication tokens.
    
    Currently handles:
    - checkout.session.completed: When a checkout is successful
    - customer.subscription.created: When a subscription is created
    - customer.subscription.updated: When a subscription is updated
    - customer.subscription.deleted: When a subscription is deleted/cancelled
    - customer.subscription.paused: When a subscription is paused
    - customer.subscription.resumed: When a subscription is resumed
    - customer.subscription.trial_will_end: When a subscription trial is about to end
    
    Returns:
        Dict with processing status
    """
    try:
        # Get the raw request body for signature verification
        payload = await request.body()
        payload_str = payload.decode("utf-8")
        
        # Get the Stripe signature from headers
        signature = request.headers.get("stripe-signature")
        
        if not signature:
            logger.warning("No Stripe signature found in webhook request")
            if response:
                response.status_code = status.HTTP_400_BAD_REQUEST
            return {
                "success": False,
                "error": "Missing Stripe signature header"
            }
        
        # Process the webhook event with raw payload and signature
        result = await stripe_service.handle_webhook_event(payload_str, signature)
        
        # Return the result
        if result is None:
            if response:
                response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            return {
                "success": False,
                "error": "Webhook processing returned None"
            }
            
        if not result.get("success", True):
            if response:
                response.status_code = status.HTTP_400_BAD_REQUEST
        return result
    except Exception as e:
        logger.error(f"Error processing Stripe webhook: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to process webhook: {str(e)}"
        }

@router.get("/subscription/portal", tags=["Subscription"])
async def create_customer_portal_session(
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Create a Stripe Customer Portal session.
    
    This endpoint creates a portal session that allows customers to manage their subscriptions,
    payment methods, and view invoice history directly in Stripe's hosted UI.
    
    Query parameters:
        return_url: Optional URL to redirect the customer after they complete their portal session
        
    Returns:
        Dict containing the portal session URL for redirect
    """
    try:
        # Get user ID from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token - missing user ID"
            )
        
        # Create portal session
        session = await stripe_service.create_customer_portal_session(
            user_id=str(user_id)
        )
        
        return {
            "success": True,
            "portal_url": session["url"]
        }
    except ValueError as e:
        logger.error(f"Value error creating portal session: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {
            "success": False,
            "error": str(e)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating portal session: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to create portal session: {str(e)}"
        }

@router.post("/subscription/checkout", tags=["Subscription"])
async def create_checkout_session(
    workspace_id: str = Body(..., description="Workspace ID to subscribe"),
    states: List[str] = Body(..., description="List of two-letter state codes (e.g., ['CA'])"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Create a checkout session for a subscription.
    
    This endpoint creates a Stripe Checkout Session for a new workspace subscription.
    The user will be redirected to Stripe's checkout page to complete payment.
    
    Request body:
        workspace_id: ID of the workspace to subscribe
        states: List of two-letter state codes (e.g., ['CA'])
        
    Returns:
        Dict containing session information including the checkout URL
    """
    try:
        # Get user ID from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid authentication token - missing user ID")
        
        # Validate required fields
        if not workspace_id:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="workspace_id is required")
            
        if not states:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="states is required")
        
        # Validate states format
        if not isinstance(states, list) or not all(isinstance(code, str) and len(code) == 2 for code in states):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="states must be a list of two-letter codes (e.g., ['CA'])")
        
        # Create checkout session using stripe service
        session_info = await stripe_service.create_checkout_session(
            user_id=str(user_id),
            workspace_id=workspace_id,
            states=states
        )
        
        return {
            "success": True,
            "session_id": session_info["session_id"],
            "checkout_url": session_info["url"],
            "quantity": session_info["quantity"]
        }
    except ValueError as e:
        logger.error(f"Value error creating checkout session: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {
            "success": False,
            "error": str(e)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating checkout session: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to create checkout session: {str(e)}"
        }

@router.post("/subscription/preview", tags=["Subscription"], response_model=None)
async def create_subscription_preview(
    workspace_id: str = Body(..., description="Workspace ID to modify"),
    added_states: List[str] = Body(default=[], description="List of two-letter state codes to add (e.g., ['CA', 'NY'])"),
    removed_states: List[str] = Body(default=[], description="List of two-letter state codes to remove"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Union[Dict[str, Any], JSONResponse]:
    """
    Preview changes to a subscription without applying them.
    
    This endpoint allows users to see the effects of adding or removing states
    from their subscription before confirming the changes. It shows the proration amount
    that will be charged immediately, rather than charging for the next billing period in advance.
    
    Request body:
        workspace_id: ID of the workspace to modify
        added_states: Optional list of two-letter state codes to add (e.g., ['CA', 'NY'])
        removed_states: Optional list of two-letter state codes to remove
        
    Returns:
        Dict containing preview information including:
        - Current and new states
        - Current and new monthly cost
        - Proration details (refund, charge, net adjustment)
        - Immediate charge (ONLY the proration amount)
        - Next period charge (will be billed on the regular billing cycle)
    """
    try:
        # Get user ID from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token - missing user ID"
            )
        
        # Validate required fields
        if not workspace_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="workspace_id is required"
            )
            
        # At least one of added_states or removed_states must be provided
        if not added_states and not removed_states:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either added_states or removed_states must be provided"
            )
        
        # Validate states format if provided
        if added_states and (not isinstance(added_states, list) or not all(isinstance(code, str) and len(code) == 2 for code in added_states)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="added_states must be a list of two-letter codes (e.g., ['CA'])"
            )
            
        if removed_states and (not isinstance(removed_states, list) or not all(isinstance(code, str) and len(code) == 2 for code in removed_states)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="removed_states must be a list of two-letter codes (e.g., ['CA'])"
            )
        
        # Preview the subscription changes
        try:
            preview_info = await stripe_service.preview_subscription_changes(
                user_id=str(user_id),
                workspace_id=workspace_id,
                added_states=added_states,
                removed_states=removed_states
            )
            
            # Check if the result indicates some states are already active
            if not preview_info.get("success", True):
                if "already_active_states" in preview_info:
                    # Return a 400 Bad Request with information about already active states
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content={
                            "success": False,
                            "error": preview_info.get("error", "Some states are already active"),
                            "already_active_states": preview_info.get("already_active_states", []),
                            "inactive_states": preview_info.get("inactive_states", [])
                        }
                    )
            
            return {
                "success": True,
                "data": preview_info
            }
        except ValueError as ve:
            # For expected validation errors
            logger.warning(f"Validation error in preview: {str(ve)}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "error": str(ve)
                }
            )
    except ValueError as e:
        logger.error(f"Value error previewing subscription changes: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {
            "success": False,
            "error": str(e)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error previewing subscription changes: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to preview subscription changes: {str(e)}"
        }

@router.post("/subscription/confirm", tags=["Subscription"], response_model=None)
async def confirm_subscription_changes(
    workspace_id: str = Body(..., description="Workspace ID to modify"),
    added_states: List[str] = Body(default=[], description="List of two-letter state codes to add (e.g., ['CA', 'NY'])"),
    removed_states: List[str] = Body(default=[], description="List of two-letter state codes to remove (e.g., ['CA', 'NY'])"),
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Union[Dict[str, Any], JSONResponse]:
    """
    Confirm and apply subscription changes, both adding and removing states in a single operation.
    
    This endpoint allows users to modify their subscription by adding and/or removing states
    in a single atomic operation. Only the proration amount will be charged immediately.
    The updated subscription amount will be charged on the next regular billing cycle.
    
    Request body:
        workspace_id: ID of the workspace to modify
        added_states: Optional list of two-letter state codes to add (e.g., ['CA', 'NY'])
        removed_states: Optional list of two-letter state codes to remove (e.g., ['CA', 'NY'])
        
    Returns:
        Dict containing comprehensive update details including added and removed states
    """
    try:
        # Get user ID from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token - missing user ID"
            )
        
        # Validate required fields
        if not workspace_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="workspace_id is required"
            )
            
        # At least one of added_states or removed_states must be provided
        if not added_states and not removed_states:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either added_states or removed_states must be provided"
            )
        
        # Validate states format if provided
        if added_states and (not isinstance(added_states, list) or not all(isinstance(code, str) and len(code) == 2 for code in added_states)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="added_states must be a list of two-letter codes (e.g., ['CA'])"
            )
            
        if removed_states and (not isinstance(removed_states, list) or not all(isinstance(code, str) and len(code) == 2 for code in removed_states)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="removed_states must be a list of two-letter codes (e.g., ['CA'])"
            )
        
        # Apply the subscription changes
        try:
            result = await stripe_service.modify_subscription(
                user_id=str(user_id),
                workspace_id=workspace_id,
                added_states=added_states,
                removed_states=removed_states
            )
            
            # Check if the result indicates a failure
            if not result.get("success", True):
                # If there are already active or invalid states, return appropriate error
                content = {
                    "success": False,
                    "error": result.get("error", "Failed to modify subscription")
                }
                
                # Include specific error details if available
                if "already_active_states" in result:
                    content["already_active_states"] = result["already_active_states"]
                
                if "invalid_removals" in result:
                    content["invalid_removals"] = result["invalid_removals"]
                
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content=content
                )
            
            return {
                "success": True,
                "data": result
            }
        except ValueError as ve:
            # For expected validation errors
            logger.warning(f"Validation error in confirm subscription: {str(ve)}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "error": str(ve)
                }
            )
    except ValueError as e:
        logger.error(f"Value error confirming subscription changes: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {
            "success": False,
            "error": str(e)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error confirming subscription changes: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to confirm subscription changes: {str(e)}"
        }

@router.get("/subscription/details", tags=["Subscription"])
async def get_subscription_details(
    workspace_id: Optional[str] = None,
    token: Dict[str, Any] = Depends(get_token_from_header),
    response: Response = DefaultResponse()
) -> Dict[str, Any]:
    """
    Get subscription details for a user.
    
    This endpoint returns subscription information for the authenticated user.
    If a workspace_id is provided, it returns details for that specific workspace.
    Otherwise, it returns details for all workspaces the user has access to.
    
    Query parameters:
        workspace_id: Optional workspace ID to filter results
        
    Returns:
        Dict containing subscription details including:
        - For each workspace: ID, name, active states, inactive states, subscription status
        - Total active subscriptions across all workspaces
    """
    try:
        # Get user ID from token
        user_id = token.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token - missing user ID"
            )
        
        # Get subscription details
        details = await stripe_service.get_subscription_details(
            user_id=str(user_id),
            workspace_id=workspace_id if workspace_id is not None else ""
        )
        
        return {
            "success": True,
            "data": details
        }
    except ValueError as e:
        logger.error(f"Value error getting subscription details: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_400_BAD_REQUEST
        return {
            "success": False,
            "error": str(e)
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting subscription details: {str(e)}")
        logger.error(f"{traceback.format_exc()}")
        if response:
            response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        return {
            "success": False,
            "error": f"Failed to get subscription details: {str(e)}"
        }
